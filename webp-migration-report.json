{"timestamp": "2025-09-03T03:44:21.820Z", "migrationComplete": true, "summary": {"totalWebPFiles": 66, "missingWebPFiles": 0, "migratedTemplates": 66, "pendingMigration": 0, "codeUpdated": true}, "details": {"cardTemplates": {"migrated": ["en/Effect.png -> Effect.webp", "en/EffectPendulum.png -> EffectPendulum.webp", "en/Fusion.png -> Fusion.webp", "en/FusionPendulum.png -> FusionPendulum.webp", "en/LDragon.png -> LDragon.webp", "en/Link.png -> Link.webp", "en/LinkPendulum.png -> LinkPendulum.webp", "en/Normal.png -> Normal.webp", "en/NormalPendulum.png -> NormalPendulum.webp", "en/Obelisk.png -> Obelisk.webp", "en/Ra.png -> <PERSON>.webp", "en/Ritual.png -> Ritual.webp", "en/RitualPendulum.png -> RitualPendulum.webp", "en/Slifer.png -> Slifer.webp", "en/Spell.png -> Spell.webp", "en/Synchro.png -> Synchro.webp", "en/SynchroPendulum.png -> SynchroPendulum.webp", "en/Token.png -> Token.webp", "en/TokenPendulum.png -> TokenPendulum.webp", "en/Trap.png -> Trap.webp", "en/Xyz.png -> Xyz.webp", "en/XyzPendulum.png -> XyzPendulum.webp", "zh/Effect.png -> Effect.webp", "zh/EffectPendulum.png -> EffectPendulum.webp", "zh/Fusion.png -> Fusion.webp", "zh/FusionPendulum.png -> FusionPendulum.webp", "zh/LDragon.png -> LDragon.webp", "zh/Link.png -> Link.webp", "zh/LinkPendulum.png -> LinkPendulum.webp", "zh/Normal.png -> Normal.webp", "zh/NormalPendulum.png -> NormalPendulum.webp", "zh/Obelisk.png -> Obelisk.webp", "zh/Ra.png -> <PERSON><PERSON>webp", "zh/Ritual.png -> Ritual.webp", "zh/RitualPendulum.png -> RitualPendulum.webp", "zh/Slifer.png -> Slifer.webp", "zh/Spell.png -> Spell.webp", "zh/Synchro.png -> Synchro.webp", "zh/SynchroPendulum.png -> SynchroPendulum.webp", "zh/Token.png -> Token.webp", "zh/TokenPendulum.png -> TokenPendulum.webp", "zh/Trap.png -> Trap.webp", "zh/Xyz.png -> Xyz.webp", "zh/XyzPendulum.png -> XyzPendulum.webp", "ja/Effect.png -> Effect.webp", "ja/EffectPendulum.png -> EffectPendulum.webp", "ja/Fusion.png -> Fusion.webp", "ja/FusionPendulum.png -> FusionPendulum.webp", "ja/LDragon.png -> LDragon.webp", "ja/Link.png -> Link.webp", "ja/LinkPendulum.png -> LinkPendulum.webp", "ja/Normal.png -> Normal.webp", "ja/NormalPendulum.png -> NormalPendulum.webp", "ja/Obelisk.png -> Obelisk.webp", "ja/Ra.png -> <PERSON><PERSON>webp", "ja/Ritual.png -> Ritual.webp", "ja/RitualPendulum.png -> RitualPendulum.webp", "ja/Slifer.png -> Slifer.webp", "ja/Spell.png -> Spell.webp", "ja/Synchro.png -> Synchro.webp", "ja/SynchroPendulum.png -> SynchroPendulum.webp", "ja/Token.png -> Token.webp", "ja/TokenPendulum.png -> TokenPendulum.webp", "ja/Trap.png -> Trap.webp", "ja/Xyz.png -> Xyz.webp", "ja/XyzPendulum.png -> XyzPendulum.webp"], "notMigrated": []}, "fileExists": {"exists": ["en/Effect.webp", "en/EffectPendulum.webp", "en/Fusion.webp", "en/FusionPendulum.webp", "en/LDragon.webp", "en/Link.webp", "en/LinkPendulum.webp", "en/Normal.webp", "en/NormalPendulum.webp", "en/Obelisk.webp", "en/Ra.webp", "en/Ritual.webp", "en/RitualPendulum.webp", "en/Slifer.webp", "en/Spell.webp", "en/Synchro.webp", "en/SynchroPendulum.webp", "en/Token.webp", "en/TokenPendulum.webp", "en/Trap.webp", "en/Xyz.webp", "en/XyzPendulum.webp", "zh/Effect.webp", "zh/EffectPendulum.webp", "zh/Fusion.webp", "zh/FusionPendulum.webp", "zh/LDragon.webp", "zh/Link.webp", "zh/LinkPendulum.webp", "zh/Normal.webp", "zh/NormalPendulum.webp", "zh/Obelisk.webp", "zh/Ra.webp", "zh/Ritual.webp", "zh/RitualPendulum.webp", "zh/Slifer.webp", "zh/Spell.webp", "zh/Synchro.webp", "zh/SynchroPendulum.webp", "zh/Token.webp", "zh/TokenPendulum.webp", "zh/Trap.webp", "zh/Xyz.webp", "zh/XyzPendulum.webp", "ja/Effect.webp", "ja/EffectPendulum.webp", "ja/Fusion.webp", "ja/FusionPendulum.webp", "ja/LDragon.webp", "ja/Link.webp", "ja/LinkPendulum.webp", "ja/Normal.webp", "ja/NormalPendulum.webp", "ja/Obelisk.webp", "ja/<PERSON>.webp", "ja/Ritual.webp", "ja/RitualPendulum.webp", "ja/Slifer.webp", "ja/Spell.webp", "ja/Synchro.webp", "ja/SynchroPendulum.webp", "ja/Token.webp", "ja/TokenPendulum.webp", "ja/Trap.webp", "ja/Xyz.webp", "ja/XyzPendulum.webp"], "missing": []}, "codeReferences": {"webp": ["Card template reference", "4 WebP references"], "png": ["9 PNG references"]}}}