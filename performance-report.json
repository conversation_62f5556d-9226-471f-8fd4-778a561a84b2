{"issues": ["JS Bundle过大: 2251KB，建议进行代码分割", "发现3个大文件需要优化", "发现69个大图片文件需要压缩", "可能存在349个未使用的CSS规则", "字体文件总大小过大: 23.36MB"], "recommendations": ["使用图片压缩工具减少图片大小", "为JPEG/PNG图片生成WebP版本以提升加载速度", "使用PurgeCSS移除未使用的CSS", "考虑字体子集化或使用系统字体", "对大字体文件进行压缩或转换为WOFF2格式", "为关键字体添加preload标签", "实施代码分割，按路由或功能拆分Bundle", "实施图片懒加载和响应式图片", "启用Gzip/Brotli压缩", "设置适当的缓存策略", "使用CDN加速静态资源", "优化图片格式（WebP/AVIF）", "实施Service Worker缓存策略"], "metrics": {"totalJSSize": 2251, "totalCSSSize": 275, "largeFiles": [{"file": "_nuxt/ba1557d.js", "size": 1545, "type": "JS"}, {"file": "_nuxt/e819957.js", "size": 278, "type": "JS"}, {"file": "_nuxt/css/934fe22.css", "size": 219, "type": "CSS"}], "totalImageSize": 687906, "imageCount": 10328, "totalFontSize": 23.36, "fontCount": 9}}