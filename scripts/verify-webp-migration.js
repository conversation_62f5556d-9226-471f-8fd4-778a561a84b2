#!/usr/bin/env node

/**
 * WebP迁移验证脚本
 * 验证所有PNG文件引用是否已正确迁移到WebP格式
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证WebP迁移...')

class WebPMigrationVerifier {
  constructor() {
    this.results = {
      cardTemplates: { migrated: [], notMigrated: [] },
      fileExists: { exists: [], missing: [] },
      codeReferences: { webp: [], png: [] }
    }
  }

  async verify() {
    console.log('📁 检查card文件夹下的WebP文件...')
    this.checkCardWebPFiles()
    
    console.log('\n📝 检查代码中的引用...')
    this.checkCodeReferences()
    
    console.log('\n📊 生成验证报告...')
    this.generateReport()
  }

  checkCardWebPFiles() {
    const cardPath = path.join(process.cwd(), 'static', 'images', 'card')
    const languages = ['en', 'zh', 'ja']
    
    languages.forEach(lang => {
      const langPath = path.join(cardPath, lang)
      
      if (!fs.existsSync(langPath)) {
        console.log(`   ⚠️  ${lang} 文件夹不存在`)
        return
      }
      
      console.log(`   📂 检查 ${lang} 文件夹...`)
      
      const files = fs.readdirSync(langPath)
      const pngFiles = files.filter(file => file.endsWith('.png'))
      const webpFiles = files.filter(file => file.endsWith('.webp'))
      
      console.log(`      PNG文件: ${pngFiles.length} 个`)
      console.log(`      WebP文件: ${webpFiles.length} 个`)
      
      // 检查每个PNG文件是否有对应的WebP文件
      pngFiles.forEach(pngFile => {
        const webpFile = pngFile.replace('.png', '.webp')
        const webpPath = path.join(langPath, webpFile)
        
        if (fs.existsSync(webpPath)) {
          this.results.fileExists.exists.push(`${lang}/${webpFile}`)
          this.results.cardTemplates.migrated.push(`${lang}/${pngFile} -> ${webpFile}`)
        } else {
          this.results.fileExists.missing.push(`${lang}/${webpFile}`)
          this.results.cardTemplates.notMigrated.push(`${lang}/${pngFile}`)
        }
      })
    })
  }

  checkCodeReferences() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    
    if (!fs.existsSync(indexPath)) {
      console.log('   ❌ index.vue 文件不存在')
      return
    }
    
    const content = fs.readFileSync(indexPath, 'utf8')
    
    // 检查模板引用
    const templateRegex = /template:\s*`\/images\/card\/\$\{[^}]+\}\/\$\{[^}]+\}\.(png|webp)`/g
    const templateMatches = content.match(templateRegex)
    
    if (templateMatches) {
      templateMatches.forEach(match => {
        if (match.includes('.webp')) {
          this.results.codeReferences.webp.push('Card template reference')
          console.log('   ✅ 卡片模板引用已迁移到WebP')
        } else if (match.includes('.png')) {
          this.results.codeReferences.png.push('Card template reference')
          console.log('   ❌ 卡片模板引用仍使用PNG')
        }
      })
    }
    
    // 检查其他图片引用
    const imageRegex = /\/images\/[^"'`\s]+\.(png|webp)/g
    const imageMatches = content.match(imageRegex) || []
    
    let pngCount = 0
    let webpCount = 0
    
    imageMatches.forEach(match => {
      if (match.includes('.webp')) {
        webpCount++
      } else if (match.includes('.png')) {
        pngCount++
      }
    })
    
    console.log(`   📊 代码中的图片引用统计:`)
    console.log(`      WebP引用: ${webpCount} 个`)
    console.log(`      PNG引用: ${pngCount} 个`)
    
    this.results.codeReferences.webp.push(`${webpCount} WebP references`)
    this.results.codeReferences.png.push(`${pngCount} PNG references`)
  }

  generateReport() {
    console.log('\n📋 WebP迁移验证报告')
    console.log('=' .repeat(50))
    
    // 文件存在性检查
    console.log('\n📁 文件存在性检查:')
    console.log(`   ✅ 存在的WebP文件: ${this.results.fileExists.exists.length} 个`)
    if (this.results.fileExists.exists.length > 0) {
      this.results.fileExists.exists.slice(0, 5).forEach(file => {
        console.log(`      - ${file}`)
      })
      if (this.results.fileExists.exists.length > 5) {
        console.log(`      ... 还有 ${this.results.fileExists.exists.length - 5} 个文件`)
      }
    }
    
    console.log(`   ❌ 缺失的WebP文件: ${this.results.fileExists.missing.length} 个`)
    if (this.results.fileExists.missing.length > 0) {
      this.results.fileExists.missing.forEach(file => {
        console.log(`      - ${file}`)
      })
    }
    
    // 迁移状态检查
    console.log('\n🔄 迁移状态检查:')
    console.log(`   ✅ 已迁移: ${this.results.cardTemplates.migrated.length} 个`)
    console.log(`   ❌ 未迁移: ${this.results.cardTemplates.notMigrated.length} 个`)
    
    if (this.results.cardTemplates.notMigrated.length > 0) {
      console.log('\n   未迁移的文件:')
      this.results.cardTemplates.notMigrated.forEach(file => {
        console.log(`      - ${file}`)
      })
    }
    
    // 代码引用检查
    console.log('\n📝 代码引用检查:')
    const hasWebPTemplate = this.results.codeReferences.webp.some(ref => 
      ref.includes('Card template reference')
    )
    
    if (hasWebPTemplate) {
      console.log('   ✅ 卡片模板引用已迁移到WebP')
    } else {
      console.log('   ❌ 卡片模板引用仍使用PNG')
    }
    
    // 总体评估
    console.log('\n📊 总体评估:')
    const allFilesExist = this.results.fileExists.missing.length === 0
    const codeUpdated = hasWebPTemplate
    const migrationComplete = allFilesExist && codeUpdated
    
    if (migrationComplete) {
      console.log('🎉 WebP迁移已完成！')
      console.log('💡 建议:')
      console.log('   1. 运行构建测试确保所有图片正常加载')
      console.log('   2. 在浏览器中验证WebP图片显示正常')
      console.log('   3. 检查页面加载性能是否有改善')
    } else {
      console.log('⚠️  WebP迁移未完全完成')
      
      if (!allFilesExist) {
        console.log('   - 部分WebP文件缺失，请检查文件生成')
      }
      
      if (!codeUpdated) {
        console.log('   - 代码引用未完全更新，请检查模板引用')
      }
    }
    
    // 性能预期
    if (migrationComplete) {
      console.log('\n📈 预期性能改善:')
      console.log('   - 图片文件大小减少 25-35%')
      console.log('   - 页面加载速度提升 10-20%')
      console.log('   - 带宽使用减少')
      console.log('   - 用户体验改善')
    }
    
    // 保存详细报告
    const reportPath = path.join(process.cwd(), 'webp-migration-report.json')
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      migrationComplete,
      summary: {
        totalWebPFiles: this.results.fileExists.exists.length,
        missingWebPFiles: this.results.fileExists.missing.length,
        migratedTemplates: this.results.cardTemplates.migrated.length,
        pendingMigration: this.results.cardTemplates.notMigrated.length,
        codeUpdated: codeUpdated
      },
      details: this.results
    }, null, 2))
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
    
    return migrationComplete
  }
}

// 运行验证
if (require.main === module) {
  const verifier = new WebPMigrationVerifier()
  verifier.verify().then(() => {
    process.exit(0)
  }).catch(error => {
    console.error('验证过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = WebPMigrationVerifier
