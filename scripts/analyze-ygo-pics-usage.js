#!/usr/bin/env node

/**
 * YGO Pics使用情况分析脚本
 * 分析ygo/pics目录下的图片是否被实际使用
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始分析YGO Pics使用情况...')

class YgoPicsAnalyzer {
  constructor() {
    this.results = {
      featureEnabled: false,
      uiVisible: false,
      totalPicsFiles: 0,
      totalPicsSize: 0,
      dataFileExists: false,
      dataFileSize: 0,
      recommendations: []
    }
  }

  async analyze() {
    console.log('📁 检查ygo/pics目录...')
    this.checkPicsDirectory()
    
    console.log('\n📊 检查数据文件...')
    this.checkDataFiles()
    
    console.log('\n🔍 分析功能使用情况...')
    this.analyzeFeatureUsage()
    
    console.log('\n📋 生成分析报告...')
    this.generateReport()
  }

  checkPicsDirectory() {
    const picsPath = path.join(process.cwd(), 'static', 'ygo', 'pics')
    
    if (!fs.existsSync(picsPath)) {
      console.log('   ❌ ygo/pics 目录不存在')
      return
    }

    const files = fs.readdirSync(picsPath)
    const jpgFiles = files.filter(file => file.endsWith('.jpg'))
    
    this.results.totalPicsFiles = jpgFiles.length
    
    // 计算总大小
    let totalSize = 0
    jpgFiles.forEach(file => {
      const filePath = path.join(picsPath, file)
      const stats = fs.statSync(filePath)
      totalSize += stats.size
    })
    
    this.results.totalPicsSize = Math.round(totalSize / 1024 / 1024) // MB
    
    console.log(`   📊 找到 ${jpgFiles.length} 个JPG文件`)
    console.log(`   💾 总大小: ${this.results.totalPicsSize} MB`)
    
    // 检查一些示例文件
    const sampleFiles = jpgFiles.slice(0, 5)
    console.log(`   📋 示例文件:`)
    sampleFiles.forEach(file => {
      const filePath = path.join(picsPath, file)
      const stats = fs.statSync(filePath)
      const sizeKB = Math.round(stats.size / 1024)
      console.log(`      - ${file} (${sizeKB} KB)`)
    })
  }

  checkDataFiles() {
    const dataPath = path.join(process.cwd(), 'static', 'ygo', 'card_data.json')
    const gzDataPath = path.join(process.cwd(), 'static', 'ygo', 'card_data.json.gz')
    
    if (fs.existsSync(dataPath)) {
      const stats = fs.statSync(dataPath)
      this.results.dataFileExists = true
      this.results.dataFileSize = Math.round(stats.size / 1024 / 1024) // MB
      console.log(`   ✅ card_data.json 存在 (${this.results.dataFileSize} MB)`)
    }
    
    if (fs.existsSync(gzDataPath)) {
      const stats = fs.statSync(gzDataPath)
      const gzSize = Math.round(stats.size / 1024 / 1024) // MB
      console.log(`   ✅ card_data.json.gz 存在 (${gzSize} MB)`)
    }
  }

  analyzeFeatureUsage() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    
    if (!fs.existsSync(indexPath)) {
      console.log('   ❌ index.vue 文件不存在')
      return
    }
    
    const content = fs.readFileSync(indexPath, 'utf8')
    
    // 检查功能是否默认启用
    const defaultEnabledMatch = content.match(/cardLoadYgoProEnabled:\s*(true|false)/)
    if (defaultEnabledMatch) {
      this.results.featureEnabled = defaultEnabledMatch[1] === 'true'
      console.log(`   🔧 功能默认状态: ${this.results.featureEnabled ? '启用' : '禁用'}`)
    }
    
    // 检查UI是否可见
    const uiHiddenMatch = content.match(/<b-row[^>]*style="display:\s*none;"[^>]*>[\s\S]*?cardLoadYgoProEnabled/)
    this.results.uiVisible = !uiHiddenMatch
    console.log(`   👁️  UI可见性: ${this.results.uiVisible ? '可见' : '隐藏'}`)
    
    // 检查实际使用的代码路径
    const usageMatch = content.match(/if\s*\(\s*this\.cardLoadYgoProEnabled\s*\)/)
    const picsUsageMatch = content.match(/\/ygo\/pics\/\$\{[^}]+\}\.jpg/)
    
    console.log(`   🔍 功能使用检查:`)
    console.log(`      - 条件判断存在: ${usageMatch ? '✅' : '❌'}`)
    console.log(`      - pics路径引用: ${picsUsageMatch ? '✅' : '❌'}`)
    
    // 分析功能的实际价值
    this.analyzeFeatureValue(content)
  }

  analyzeFeatureValue(content) {
    console.log(`   📈 功能价值分析:`)
    
    // 检查是否有用户可以输入cardKey的地方
    const cardKeyInputMatch = content.match(/v-model="cardKey"/)
    console.log(`      - 用户可输入卡片ID: ${cardKeyInputMatch ? '✅' : '❌'}`)
    
    // 检查load_ygopro_data方法的复杂度
    const loadMethodMatch = content.match(/async load_ygopro_data\(key\)[\s\S]*?return true/)
    if (loadMethodMatch) {
      const methodContent = loadMethodMatch[0]
      const linesCount = methodContent.split('\n').length
      console.log(`      - 数据加载方法复杂度: ${linesCount} 行代码`)
      
      // 检查方法是否设置了很多字段
      const fieldSetCount = (methodContent.match(/this\.\w+\s*=/g) || []).length
      console.log(`      - 自动填充字段数量: ${fieldSetCount} 个`)
    }
  }

  generateReport() {
    console.log('\n📋 YGO Pics使用情况分析报告')
    console.log('=' .repeat(60))
    
    // 基本信息
    console.log('\n📊 资源统计:')
    console.log(`   图片文件数量: ${this.results.totalPicsFiles} 个`)
    console.log(`   图片总大小: ${this.results.totalPicsSize} MB`)
    console.log(`   数据文件大小: ${this.results.dataFileSize} MB`)
    console.log(`   总资源大小: ${this.results.totalPicsSize + this.results.dataFileSize} MB`)
    
    // 功能状态
    console.log('\n🔧 功能状态:')
    console.log(`   默认启用: ${this.results.featureEnabled ? '✅' : '❌'}`)
    console.log(`   UI可见: ${this.results.uiVisible ? '✅' : '❌'}`)
    console.log(`   数据文件存在: ${this.results.dataFileExists ? '✅' : '❌'}`)
    
    // 使用情况评估
    console.log('\n📈 使用情况评估:')
    
    const isActuallyUsed = this.results.featureEnabled && this.results.dataFileExists
    const isUserAccessible = this.results.uiVisible
    const hasSignificantImpact = this.results.totalPicsSize > 100 // 超过100MB认为有显著影响
    
    if (!isUserAccessible) {
      console.log('   ⚠️  功能UI被隐藏，用户无法访问')
      this.results.recommendations.push('UI被隐藏，用户无法使用此功能')
    }
    
    if (!isActuallyUsed) {
      console.log('   ⚠️  功能未被实际使用')
      this.results.recommendations.push('功能默认禁用或数据文件缺失')
    }
    
    if (hasSignificantImpact) {
      console.log(`   ⚠️  资源占用过大 (${this.results.totalPicsSize + this.results.dataFileSize} MB)`)
      this.results.recommendations.push('资源文件占用大量存储空间和带宽')
    }
    
    // 性能影响分析
    console.log('\n🚀 性能影响分析:')
    const estimatedLoadTime = Math.round((this.results.totalPicsSize + this.results.dataFileSize) / 10) // 假设10MB/s
    console.log(`   预估下载时间: ${estimatedLoadTime} 秒 (10MB/s网速)`)
    console.log(`   CDN带宽成本: 显著增加`)
    console.log(`   存储空间占用: ${this.results.totalPicsSize + this.results.dataFileSize} MB`)
    
    // 建议
    console.log('\n💡 优化建议:')
    
    if (!isUserAccessible && !isActuallyUsed) {
      console.log('   🗑️  建议移除ygo/pics目录和相关功能')
      console.log('   📉 预期收益:')
      console.log(`      - 减少 ${this.results.totalPicsSize} MB 图片资源`)
      console.log(`      - 减少 ${this.results.dataFileSize} MB 数据文件`)
      console.log(`      - 降低CDN带宽成本`)
      console.log(`      - 减少构建和部署时间`)
      console.log(`      - 简化代码维护`)
      
      this.results.recommendations.push('完全移除未使用的YGO Pics功能')
    } else if (isUserAccessible || isActuallyUsed) {
      console.log('   🔄 建议优化策略:')
      console.log('      - 实现按需加载 (仅在用户需要时下载)')
      console.log('      - 图片压缩和WebP转换')
      console.log('      - 使用CDN缓存策略')
      console.log('      - 考虑外部API替代本地存储')
      
      this.results.recommendations.push('优化资源加载策略')
    }
    
    // 代码清理建议
    if (!isActuallyUsed) {
      console.log('\n🧹 代码清理建议:')
      console.log('   需要移除的代码:')
      console.log('      - cardLoadYgoProEnabled 相关变量和方法')
      console.log('      - load_ygopro_data 方法')
      console.log('      - loadYgoproData 方法')
      console.log('      - ygo/pics 路径引用')
      console.log('      - 相关UI组件 (已隐藏)')
    }
    
    // 总结
    console.log('\n📋 总结:')
    const shouldRemove = !isUserAccessible && !isActuallyUsed && hasSignificantImpact
    
    if (shouldRemove) {
      console.log('🎯 强烈建议移除YGO Pics功能:')
      console.log('   ✅ 功能未被用户使用 (UI隐藏)')
      console.log('   ✅ 占用大量资源空间')
      console.log('   ✅ 增加不必要的维护成本')
      console.log('   ✅ 影响网站加载性能')
    } else {
      console.log('🔄 建议优化YGO Pics功能而非移除')
    }
    
    // 保存详细报告
    const reportPath = path.join(process.cwd(), 'ygo-pics-analysis-report.json')
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      shouldRemove,
      summary: {
        totalFiles: this.results.totalPicsFiles,
        totalSizeMB: this.results.totalPicsSize + this.results.dataFileSize,
        featureEnabled: this.results.featureEnabled,
        uiVisible: this.results.uiVisible,
        actuallyUsed: isActuallyUsed,
        userAccessible: isUserAccessible
      },
      recommendations: this.results.recommendations,
      details: this.results
    }, null, 2))
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
    
    return shouldRemove
  }
}

// 运行分析
if (require.main === module) {
  const analyzer = new YgoPicsAnalyzer()
  analyzer.analyze().then(() => {
    process.exit(0)
  }).catch(error => {
    console.error('分析过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = YgoPicsAnalyzer
