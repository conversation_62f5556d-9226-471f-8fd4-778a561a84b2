#!/usr/bin/env node

/**
 * 性能优化脚本
 * 分析和优化页面性能瓶颈
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始性能优化分析...')

class PerformanceOptimizer {
  constructor() {
    this.distPath = path.join(process.cwd(), 'dist')
    this.staticPath = path.join(process.cwd(), 'static')
    this.optimizationReport = {
      issues: [],
      recommendations: [],
      metrics: {}
    }
  }

  async analyze() {
    console.log('📊 分析构建产物...')
    
    // 1. 分析Bundle大小
    this.analyzeBundleSize()
    
    // 2. 分析图片优化机会
    this.analyzeImages()
    
    // 3. 分析CSS优化机会
    this.analyzeCSS()
    
    // 4. 分析字体加载
    this.analyzeFonts()
    
    // 5. 分析关键资源
    this.analyzeCriticalResources()
    
    // 6. 生成优化建议
    this.generateRecommendations()
    
    // 7. 输出报告
    this.outputReport()
  }

  analyzeBundleSize() {
    console.log('📦 分析Bundle大小...')
    
    if (!fs.existsSync(this.distPath)) {
      this.optimizationReport.issues.push('构建目录不存在，请先运行构建命令')
      return
    }

    const jsFiles = this.findFiles(this.distPath, '.js')
    const cssFiles = this.findFiles(this.distPath, '.css')
    
    let totalJSSize = 0
    let totalCSSSize = 0
    let largeFiles = []

    jsFiles.forEach(file => {
      const stats = fs.statSync(file)
      const sizeKB = Math.round(stats.size / 1024)
      totalJSSize += sizeKB
      
      if (sizeKB > 200) {
        largeFiles.push({
          file: path.relative(this.distPath, file),
          size: sizeKB,
          type: 'JS'
        })
      }
    })

    cssFiles.forEach(file => {
      const stats = fs.statSync(file)
      const sizeKB = Math.round(stats.size / 1024)
      totalCSSSize += sizeKB
      
      if (sizeKB > 100) {
        largeFiles.push({
          file: path.relative(this.distPath, file),
          size: sizeKB,
          type: 'CSS'
        })
      }
    })

    this.optimizationReport.metrics.totalJSSize = totalJSSize
    this.optimizationReport.metrics.totalCSSSize = totalCSSSize
    this.optimizationReport.metrics.largeFiles = largeFiles

    if (totalJSSize > 1000) {
      this.optimizationReport.issues.push(`JS Bundle过大: ${totalJSSize}KB，建议进行代码分割`)
    }

    if (largeFiles.length > 0) {
      this.optimizationReport.issues.push(`发现${largeFiles.length}个大文件需要优化`)
    }
  }

  analyzeImages() {
    console.log('🖼️ 分析图片优化机会...')
    
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']
    const imageFiles = []
    
    // 检查static目录中的图片
    if (fs.existsSync(this.staticPath)) {
      imageExtensions.forEach(ext => {
        const files = this.findFiles(this.staticPath, ext)
        imageFiles.push(...files)
      })
    }

    let totalImageSize = 0
    let largeImages = []
    let unoptimizedImages = []

    imageFiles.forEach(file => {
      const stats = fs.statSync(file)
      const sizeKB = Math.round(stats.size / 1024)
      totalImageSize += sizeKB
      
      if (sizeKB > 500) {
        largeImages.push({
          file: path.relative(process.cwd(), file),
          size: sizeKB
        })
      }
      
      // 检查是否有WebP版本
      const ext = path.extname(file)
      if (['.jpg', '.jpeg', '.png'].includes(ext)) {
        const webpPath = file.replace(ext, '.webp')
        if (!fs.existsSync(webpPath)) {
          unoptimizedImages.push(path.relative(process.cwd(), file))
        }
      }
    })

    this.optimizationReport.metrics.totalImageSize = totalImageSize
    this.optimizationReport.metrics.imageCount = imageFiles.length

    if (largeImages.length > 0) {
      this.optimizationReport.issues.push(`发现${largeImages.length}个大图片文件需要压缩`)
      this.optimizationReport.recommendations.push('使用图片压缩工具减少图片大小')
    }

    if (unoptimizedImages.length > 0) {
      this.optimizationReport.recommendations.push('为JPEG/PNG图片生成WebP版本以提升加载速度')
    }
  }

  analyzeCSS() {
    console.log('🎨 分析CSS优化机会...')
    
    const cssFiles = this.findFiles(this.distPath, '.css')
    let unusedCSS = 0
    let duplicateRules = 0

    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8')
      
      // 简单检测未使用的CSS（基于常见模式）
      const rules = content.match(/[^{}]+{[^{}]*}/g) || []
      const selectors = content.match(/[^{}]+(?={)/g) || []
      
      // 检测可能未使用的选择器
      const suspiciousSelectors = selectors.filter(selector => 
        selector.includes(':hover:active') || 
        selector.includes('::before::after') ||
        selector.match(/\.[a-z]+-[a-z]+-[a-z]+-[a-z]+/)
      )
      
      unusedCSS += suspiciousSelectors.length
    })

    if (unusedCSS > 10) {
      this.optimizationReport.issues.push(`可能存在${unusedCSS}个未使用的CSS规则`)
      this.optimizationReport.recommendations.push('使用PurgeCSS移除未使用的CSS')
    }
  }

  analyzeFonts() {
    console.log('🔤 分析字体加载优化...')
    
    const fontPath = path.join(this.staticPath, 'fonts')
    if (!fs.existsSync(fontPath)) return

    const fontFiles = this.findFiles(fontPath, '.ttf')
      .concat(this.findFiles(fontPath, '.otf'))
      .concat(this.findFiles(fontPath, '.woff'))
      .concat(this.findFiles(fontPath, '.woff2'))

    let totalFontSize = 0
    let largeFonts = []

    fontFiles.forEach(file => {
      const stats = fs.statSync(file)
      const sizeMB = Math.round(stats.size / 1024 / 1024 * 100) / 100
      totalFontSize += sizeMB
      
      if (sizeMB > 1) {
        largeFonts.push({
          file: path.relative(process.cwd(), file),
          size: sizeMB
        })
      }
    })

    this.optimizationReport.metrics.totalFontSize = totalFontSize
    this.optimizationReport.metrics.fontCount = fontFiles.length

    if (totalFontSize > 10) {
      this.optimizationReport.issues.push(`字体文件总大小过大: ${totalFontSize}MB`)
      this.optimizationReport.recommendations.push('考虑字体子集化或使用系统字体')
    }

    if (largeFonts.length > 0) {
      this.optimizationReport.recommendations.push('对大字体文件进行压缩或转换为WOFF2格式')
    }
  }

  analyzeCriticalResources() {
    console.log('⚡ 分析关键资源加载...')
    
    const indexPath = path.join(this.distPath, 'index.html')
    if (!fs.existsSync(indexPath)) return

    const content = fs.readFileSync(indexPath, 'utf8')
    
    // 检查关键CSS是否内联
    const hasInlineCSS = content.includes('<style>')
    const hasPreloadCSS = content.includes('rel="preload"') && content.includes('as="style"')
    const hasPreloadFonts = content.includes('rel="preload"') && content.includes('as="font"')
    
    if (!hasInlineCSS) {
      this.optimizationReport.recommendations.push('考虑内联关键CSS以减少渲染阻塞')
    }
    
    if (!hasPreloadCSS) {
      this.optimizationReport.recommendations.push('为关键CSS添加preload标签')
    }
    
    if (!hasPreloadFonts) {
      this.optimizationReport.recommendations.push('为关键字体添加preload标签')
    }
  }

  generateRecommendations() {
    console.log('💡 生成优化建议...')
    
    const { metrics } = this.optimizationReport
    
    // 基于分析结果生成具体建议
    if (metrics.totalJSSize > 500) {
      this.optimizationReport.recommendations.push('实施代码分割，按路由或功能拆分Bundle')
    }
    
    if (metrics.totalImageSize > 5000) {
      this.optimizationReport.recommendations.push('实施图片懒加载和响应式图片')
    }
    
    if (metrics.fontCount > 10) {
      this.optimizationReport.recommendations.push('减少字体文件数量，使用字体显示策略')
    }
    
    // 添加通用优化建议
    this.optimizationReport.recommendations.push(
      '启用Gzip/Brotli压缩',
      '设置适当的缓存策略',
      '使用CDN加速静态资源',
      '优化图片格式（WebP/AVIF）',
      '实施Service Worker缓存策略'
    )
  }

  outputReport() {
    console.log('\n📋 性能优化报告')
    console.log('=' .repeat(50))
    
    console.log('\n📊 性能指标:')
    console.log(`   JS Bundle总大小: ${this.optimizationReport.metrics.totalJSSize || 0}KB`)
    console.log(`   CSS总大小: ${this.optimizationReport.metrics.totalCSSSize || 0}KB`)
    console.log(`   图片总大小: ${this.optimizationReport.metrics.totalImageSize || 0}KB`)
    console.log(`   字体总大小: ${this.optimizationReport.metrics.totalFontSize || 0}MB`)
    
    if (this.optimizationReport.issues.length > 0) {
      console.log('\n⚠️  发现的问题:')
      this.optimizationReport.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`)
      })
    }
    
    if (this.optimizationReport.recommendations.length > 0) {
      console.log('\n💡 优化建议:')
      this.optimizationReport.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`)
      })
    }
    
    // 保存报告到文件
    const reportPath = path.join(process.cwd(), 'performance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(this.optimizationReport, null, 2))
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  }

  findFiles(dir, extension) {
    const files = []
    
    if (!fs.existsSync(dir)) return files
    
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files.push(...this.findFiles(fullPath, extension))
      } else if (item.endsWith(extension)) {
        files.push(fullPath)
      }
    }
    
    return files
  }
}

// 运行优化分析
if (require.main === module) {
  const optimizer = new PerformanceOptimizer()
  optimizer.analyze().catch(console.error)
}

module.exports = PerformanceOptimizer
