#!/usr/bin/env node

/**
 * 移除未使用的YGO资源脚本
 * 安全地移除ygo/pics目录和相关的未使用功能代码
 */

const fs = require('fs')
const path = require('path')

console.log('🗑️  开始移除未使用的YGO资源...')

class YgoResourceRemover {
  constructor() {
    this.backupDir = path.join(process.cwd(), 'backup-ygo-resources')
    this.removedResources = {
      directories: [],
      files: [],
      codeChanges: [],
      totalSizeMB: 0
    }
  }

  async remove() {
    console.log('📋 创建备份目录...')
    this.createBackup()
    
    console.log('\n🗂️  移除资源文件...')
    this.removeResourceFiles()
    
    console.log('\n📝 清理代码引用...')
    this.cleanupCodeReferences()
    
    console.log('\n📊 生成移除报告...')
    this.generateReport()
  }

  createBackup() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true })
      console.log(`   ✅ 备份目录已创建: ${this.backupDir}`)
    }
  }

  removeResourceFiles() {
    const ygoDir = path.join(process.cwd(), 'static', 'ygo')
    
    if (!fs.existsSync(ygoDir)) {
      console.log('   ⚠️  ygo目录不存在，跳过文件移除')
      return
    }

    // 备份整个ygo目录
    const backupYgoDir = path.join(this.backupDir, 'ygo')
    this.copyDirectory(ygoDir, backupYgoDir)
    console.log(`   💾 已备份ygo目录到: ${backupYgoDir}`)

    // 计算要移除的文件大小
    const picsDir = path.join(ygoDir, 'pics')
    const dataFile = path.join(ygoDir, 'card_data.json')
    const gzDataFile = path.join(ygoDir, 'card_data.json.gz')
    
    let totalSize = 0
    
    // 计算pics目录大小
    if (fs.existsSync(picsDir)) {
      totalSize += this.calculateDirectorySize(picsDir)
      console.log(`   📁 pics目录大小: ${Math.round(this.calculateDirectorySize(picsDir) / 1024 / 1024)} MB`)
    }
    
    // 计算数据文件大小
    if (fs.existsSync(dataFile)) {
      const stats = fs.statSync(dataFile)
      totalSize += stats.size
      console.log(`   📄 card_data.json大小: ${Math.round(stats.size / 1024 / 1024)} MB`)
    }
    
    if (fs.existsSync(gzDataFile)) {
      const stats = fs.statSync(gzDataFile)
      totalSize += stats.size
      console.log(`   📄 card_data.json.gz大小: ${Math.round(stats.size / 1024 / 1024)} MB`)
    }
    
    this.removedResources.totalSizeMB = Math.round(totalSize / 1024 / 1024)
    
    // 移除文件和目录
    if (fs.existsSync(picsDir)) {
      this.removeDirectory(picsDir)
      this.removedResources.directories.push('static/ygo/pics')
      console.log('   ✅ 已移除pics目录')
    }
    
    if (fs.existsSync(dataFile)) {
      fs.unlinkSync(dataFile)
      this.removedResources.files.push('static/ygo/card_data.json')
      console.log('   ✅ 已移除card_data.json')
    }
    
    if (fs.existsSync(gzDataFile)) {
      fs.unlinkSync(gzDataFile)
      this.removedResources.files.push('static/ygo/card_data.json.gz')
      console.log('   ✅ 已移除card_data.json.gz')
    }
    
    // 检查是否还有其他文件
    const remainingFiles = fs.readdirSync(ygoDir)
    if (remainingFiles.length === 0) {
      fs.rmdirSync(ygoDir)
      this.removedResources.directories.push('static/ygo')
      console.log('   ✅ 已移除空的ygo目录')
    } else {
      console.log(`   ℹ️  ygo目录中还有其他文件: ${remainingFiles.join(', ')}`)
    }
  }

  cleanupCodeReferences() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    
    if (!fs.existsSync(indexPath)) {
      console.log('   ❌ index.vue 文件不存在')
      return
    }
    
    // 备份原文件
    const backupIndexPath = path.join(this.backupDir, 'index.vue.backup')
    fs.copyFileSync(indexPath, backupIndexPath)
    console.log(`   💾 已备份index.vue到: ${backupIndexPath}`)
    
    let content = fs.readFileSync(indexPath, 'utf8')
    let changesMade = 0
    
    // 1. 移除隐藏的UI区域（整个b-row）
    const hiddenRowRegex = /<b-row class="my-3" style="display: none;">[\s\S]*?<\/b-row>/
    if (hiddenRowRegex.test(content)) {
      content = content.replace(hiddenRowRegex, '')
      changesMade++
      this.removedResources.codeChanges.push('移除隐藏的YGO Pro UI区域')
      console.log('   ✅ 已移除隐藏的UI区域')
    }
    
    // 2. 移除cardLoadYgoProEnabled相关的数据属性
    content = content.replace(/cardLoadYgoProEnabled:\s*true,?\s*\n/, '')
    content = content.replace(/cardKey:\s*'',?\s*\n/, '')
    changesMade += 2
    this.removedResources.codeChanges.push('移除cardLoadYgoProEnabled和cardKey数据属性')
    console.log('   ✅ 已移除相关数据属性')
    
    // 3. 移除load_default_data中的相关设置
    content = content.replace(/this\.cardLoadYgoProEnabled\s*=\s*true\s*\n/, '')
    content = content.replace(/this\.cardKey\s*=\s*""\s*\n/, '')
    changesMade += 2
    this.removedResources.codeChanges.push('移除load_default_data中的相关设置')
    console.log('   ✅ 已清理load_default_data方法')
    
    // 4. 移除YGO Pro数据加载的条件判断
    const ygoProConditionRegex = /if\s*\(\s*this\.cardLoadYgoProEnabled\s*\)\s*\{[\s\S]*?\}\s*catch[\s\S]*?\}\s*\}/
    if (ygoProConditionRegex.test(content)) {
      content = content.replace(ygoProConditionRegex, '')
      changesMade++
      this.removedResources.codeChanges.push('移除YGO Pro数据加载条件判断')
      console.log('   ✅ 已移除YGO Pro数据加载逻辑')
    }
    
    // 5. 移除load_ygopro_data方法
    const loadYgoProMethodRegex = /\/\/\s*載入YGOPRO2資料[\s\S]*?async\s+load_ygopro_data\([^)]*\)\s*\{[\s\S]*?\n\s*\},?\s*\n/
    if (loadYgoProMethodRegex.test(content)) {
      content = content.replace(loadYgoProMethodRegex, '')
      changesMade++
      this.removedResources.codeChanges.push('移除load_ygopro_data方法')
      console.log('   ✅ 已移除load_ygopro_data方法')
    }
    
    // 6. 移除loadYgoproData方法
    const loadYgoproDataMethodRegex = /\/\*\*[\s\S]*?\*\/[\s\S]*?async\s+loadYgoproData\(\)\s*\{[\s\S]*?\n\s*\},?\s*\n/
    if (loadYgoproDataMethodRegex.test(content)) {
      content = content.replace(loadYgoproDataMethodRegex, '')
      changesMade++
      this.removedResources.codeChanges.push('移除loadYgoproData方法')
      console.log('   ✅ 已移除loadYgoproData方法')
    }
    
    // 7. 移除相关的数据属性声明
    content = content.replace(/ygoproData:\s*\{\},?\s*\n/, '')
    content = content.replace(/ygoproDataLoaded:\s*false,?\s*\n/, '')
    content = content.replace(/ygoproDataLoading:\s*false,?\s*\n/, '')
    changesMade += 3
    this.removedResources.codeChanges.push('移除YGO Pro相关数据属性')
    console.log('   ✅ 已移除YGO Pro相关数据属性')
    
    // 8. 移除cardKey的watch
    const cardKeyWatchRegex = /cardKey\(\)\s*\{[\s\S]*?\},?\s*\n/
    if (cardKeyWatchRegex.test(content)) {
      content = content.replace(cardKeyWatchRegex, '')
      changesMade++
      this.removedResources.codeChanges.push('移除cardKey的watch方法')
      console.log('   ✅ 已移除cardKey的watch方法')
    }
    
    // 保存修改后的文件
    if (changesMade > 0) {
      fs.writeFileSync(indexPath, content)
      console.log(`   ✅ 已保存代码更改 (${changesMade} 处修改)`)
    }
  }

  // 辅助方法
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true })
    }
    
    const items = fs.readdirSync(src)
    items.forEach(item => {
      const srcPath = path.join(src, item)
      const destPath = path.join(dest, item)
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath)
      } else {
        fs.copyFileSync(srcPath, destPath)
      }
    })
  }

  removeDirectory(dir) {
    if (fs.existsSync(dir)) {
      const items = fs.readdirSync(dir)
      items.forEach(item => {
        const itemPath = path.join(dir, item)
        if (fs.statSync(itemPath).isDirectory()) {
          this.removeDirectory(itemPath)
        } else {
          fs.unlinkSync(itemPath)
        }
      })
      fs.rmdirSync(dir)
    }
  }

  calculateDirectorySize(dir) {
    let totalSize = 0
    const items = fs.readdirSync(dir)
    
    items.forEach(item => {
      const itemPath = path.join(dir, item)
      const stats = fs.statSync(itemPath)
      
      if (stats.isDirectory()) {
        totalSize += this.calculateDirectorySize(itemPath)
      } else {
        totalSize += stats.size
      }
    })
    
    return totalSize
  }

  generateReport() {
    console.log('\n📋 YGO资源移除报告')
    console.log('=' .repeat(50))
    
    console.log('\n🗑️  已移除的资源:')
    console.log(`   总大小: ${this.removedResources.totalSizeMB} MB`)
    
    if (this.removedResources.directories.length > 0) {
      console.log(`   目录 (${this.removedResources.directories.length} 个):`)
      this.removedResources.directories.forEach(dir => {
        console.log(`      - ${dir}`)
      })
    }
    
    if (this.removedResources.files.length > 0) {
      console.log(`   文件 (${this.removedResources.files.length} 个):`)
      this.removedResources.files.forEach(file => {
        console.log(`      - ${file}`)
      })
    }
    
    console.log('\n📝 代码清理:')
    this.removedResources.codeChanges.forEach((change, index) => {
      console.log(`   ${index + 1}. ${change}`)
    })
    
    console.log('\n💾 备份信息:')
    console.log(`   备份目录: ${this.backupDir}`)
    console.log('   如需恢复，请从备份目录复制文件')
    
    console.log('\n📈 预期收益:')
    console.log(`   ✅ 减少 ${this.removedResources.totalSizeMB} MB 存储空间`)
    console.log('   ✅ 降低CDN带宽成本')
    console.log('   ✅ 减少构建和部署时间')
    console.log('   ✅ 简化代码维护')
    console.log('   ✅ 提升网站加载性能')
    
    console.log('\n🔄 后续步骤:')
    console.log('   1. 运行构建测试: npm run build:cloudflare-simple')
    console.log('   2. 验证功能正常: npm run test:layout')
    console.log('   3. 部署到生产环境')
    console.log('   4. 监控性能改善效果')
    
    // 保存详细报告
    const reportPath = path.join(process.cwd(), 'ygo-resources-removal-report.json')
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      removedResources: this.removedResources,
      backupLocation: this.backupDir,
      totalSizeMB: this.removedResources.totalSizeMB
    }, null, 2))
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
    
    return true
  }
}

// 运行移除
if (require.main === module) {
  const remover = new YgoResourceRemover()
  remover.remove().then(() => {
    console.log('\n🎉 YGO资源移除完成！')
    process.exit(0)
  }).catch(error => {
    console.error('移除过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = YgoResourceRemover
