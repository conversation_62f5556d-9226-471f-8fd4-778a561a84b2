#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Verifies that all recent changes are properly deployed and working
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Starting deployment verification...')

// 1. Verify FAQ collapsible functionality
function verifyFAQFunctionality() {
  console.log('📋 Verifying FAQ collapsible functionality...')
  
  const indexPath = path.join(process.cwd(), 'dist', 'index.html')
  if (!fs.existsSync(indexPath)) {
    console.error('❌ index.html not found in dist directory')
    return false
  }

  try {
    const content = fs.readFileSync(indexPath, 'utf8')
    
    // Check for FAQ structure
    const hasFAQStructure = content.includes('faq-item') && 
                           content.includes('faq-question') && 
                           content.includes('faq-answer-wrapper')
    
    // Check for toggle functionality
    const hasToggleFunction = content.includes('toggleFaq') || 
                             content.includes('activeFaqIndex')
    
    // Check for chevron icons
    const hasChevronIcons = content.includes('chevron-up') || 
                           content.includes('chevron-down')

    if (hasFAQStructure && hasToggleFunction && hasChevronIcons) {
      console.log('✅ FAQ collapsible functionality verified')
      return true
    } else {
      console.error('❌ FAQ collapsible functionality incomplete:')
      console.error(`   - FAQ Structure: ${hasFAQStructure ? '✅' : '❌'}`)
      console.error(`   - Toggle Function: ${hasToggleFunction ? '✅' : '❌'}`)
      console.error(`   - Chevron Icons: ${hasChevronIcons ? '✅' : '❌'}`)
      return false
    }
  } catch (error) {
    console.error(`❌ Error reading index.html: ${error.message}`)
    return false
  }
}

// 2. Verify layout consistency
function verifyLayoutConsistency() {
  console.log('📐 Verifying layout consistency...')
  
  const distPath = path.join(process.cwd(), 'dist')
  
  // Find CSS files
  function findCssFiles(dir) {
    const files = []
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files.push(...findCssFiles(fullPath))
      } else if (item.endsWith('.css')) {
        files.push(fullPath)
      }
    }
    
    return files
  }

  try {
    const cssFiles = findCssFiles(distPath)
    let hasHeightConsistency = false
    let hasResponsiveRules = false

    cssFiles.forEach(filePath => {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for height consistency rules
      if (content.includes('min-height:85vh') || content.includes('min-height: 85vh')) {
        hasHeightConsistency = true
      }
      
      // Check for responsive media queries
      if (content.includes('@media') && content.includes('max-width')) {
        hasResponsiveRules = true
      }
    })

    if (hasHeightConsistency && hasResponsiveRules) {
      console.log('✅ Layout consistency verified')
      return true
    } else {
      console.error('❌ Layout consistency issues:')
      console.error(`   - Height Consistency: ${hasHeightConsistency ? '✅' : '❌'}`)
      console.error(`   - Responsive Rules: ${hasResponsiveRules ? '✅' : '❌'}`)
      return false
    }
  } catch (error) {
    console.error(`❌ Error verifying layout: ${error.message}`)
    return false
  }
}

// 3. Verify FontAwesome icons
function verifyFontAwesome() {
  console.log('🎨 Verifying FontAwesome icons...')
  
  const indexPath = path.join(process.cwd(), 'dist', 'index.html')
  
  try {
    const content = fs.readFileSync(indexPath, 'utf8')
    
    // Check for FontAwesome component usage
    const hasFAComponent = content.includes('<fa ') || content.includes('font-awesome-icon')
    
    // Check for proper icon syntax
    const hasProperSyntax = content.includes("['fas',") || content.includes('"fas"')
    
    // Check for chevron icons specifically
    const hasChevronIcons = content.includes('chevron-up') && content.includes('chevron-down')

    if (hasFAComponent && hasProperSyntax && hasChevronIcons) {
      console.log('✅ FontAwesome icons verified')
      return true
    } else {
      console.error('❌ FontAwesome icon issues:')
      console.error(`   - FA Component: ${hasFAComponent ? '✅' : '❌'}`)
      console.error(`   - Proper Syntax: ${hasProperSyntax ? '✅' : '❌'}`)
      console.error(`   - Chevron Icons: ${hasChevronIcons ? '✅' : '❌'}`)
      return false
    }
  } catch (error) {
    console.error(`❌ Error verifying FontAwesome: ${error.message}`)
    return false
  }
}

// 4. Verify multilingual content
function verifyMultilingualContent() {
  console.log('🌐 Verifying multilingual content...')
  
  const distPath = path.join(process.cwd(), 'dist')
  const expectedLanguages = ['en', 'zh', 'ja', 'de', 'fr', 'es', 'ko', 'pt', 'ru', 'vi', 'th', 'el']
  
  let foundLanguages = 0
  
  // Check for language-specific directories or files
  expectedLanguages.forEach(lang => {
    const langPath = path.join(distPath, lang)
    if (fs.existsSync(langPath)) {
      foundLanguages++
    }
  })

  // Also check if index.html contains multilingual meta tags
  const indexPath = path.join(distPath, 'index.html')
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf8')
    const hasHreflang = content.includes('hreflang=')
    const hasMultipleLanguages = expectedLanguages.some(lang => content.includes(`hreflang="${lang}"`))
    
    if (hasHreflang && hasMultipleLanguages) {
      console.log('✅ Multilingual content verified')
      return true
    }
  }

  if (foundLanguages >= 6) { // At least half of the languages should be present
    console.log(`✅ Multilingual content partially verified (${foundLanguages}/${expectedLanguages.length} languages)`)
    return true
  } else {
    console.error(`❌ Multilingual content incomplete (${foundLanguages}/${expectedLanguages.length} languages found)`)
    return false
  }
}

// 5. Verify asset loading
function verifyAssetLoading() {
  console.log('📦 Verifying asset loading...')
  
  const distPath = path.join(process.cwd(), 'dist')
  const nuxtPath = path.join(distPath, '_nuxt')
  
  if (!fs.existsSync(nuxtPath)) {
    console.error('❌ _nuxt directory not found')
    return false
  }

  // Check for essential assets
  const essentialAssets = {
    js: false,
    css: false,
    fonts: false,
    images: false
  }

  function checkAssets(dir) {
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        checkAssets(fullPath)
      } else {
        if (item.endsWith('.js')) essentialAssets.js = true
        if (item.endsWith('.css')) essentialAssets.css = true
        if (item.match(/\.(woff|woff2|ttf|eot)$/)) essentialAssets.fonts = true
        if (item.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) essentialAssets.images = true
      }
    }
  }

  try {
    checkAssets(nuxtPath)
    
    const allAssetsPresent = Object.values(essentialAssets).every(present => present)
    
    if (allAssetsPresent) {
      console.log('✅ Asset loading verified')
      return true
    } else {
      console.error('❌ Missing essential assets:')
      Object.entries(essentialAssets).forEach(([type, present]) => {
        console.error(`   - ${type.toUpperCase()}: ${present ? '✅' : '❌'}`)
      })
      return false
    }
  } catch (error) {
    console.error(`❌ Error verifying assets: ${error.message}`)
    return false
  }
}

// 6. Generate deployment report
function generateDeploymentReport(results) {
  console.log('\n📊 Deployment Verification Report')
  console.log('=====================================')
  
  const totalChecks = Object.keys(results).length
  const passedChecks = Object.values(results).filter(result => result).length
  const successRate = Math.round((passedChecks / totalChecks) * 100)
  
  console.log(`Overall Success Rate: ${successRate}% (${passedChecks}/${totalChecks})`)
  console.log('')
  
  Object.entries(results).forEach(([check, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${check}`)
  })
  
  console.log('')
  
  if (successRate === 100) {
    console.log('🎉 All deployment checks passed! The application should work correctly in production.')
  } else if (successRate >= 80) {
    console.log('⚠️  Most checks passed, but some issues were found. Review the failed checks above.')
  } else {
    console.log('❌ Multiple deployment issues detected. Please address the failed checks before deploying.')
  }
  
  return successRate >= 80
}

// Main execution
async function main() {
  try {
    const results = {
      'FAQ Collapsible Functionality': verifyFAQFunctionality(),
      'Layout Consistency': verifyLayoutConsistency(),
      'FontAwesome Icons': verifyFontAwesome(),
      'Multilingual Content': verifyMultilingualContent(),
      'Asset Loading': verifyAssetLoading()
    }
    
    const deploymentReady = generateDeploymentReport(results)
    
    if (deploymentReady) {
      console.log('\n✅ Deployment verification completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Deployment verification failed. Please fix the issues above.')
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Error during deployment verification:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { 
  verifyFAQFunctionality, 
  verifyLayoutConsistency, 
  verifyFontAwesome, 
  verifyMultilingualContent, 
  verifyAssetLoading,
  generateDeploymentReport
}
