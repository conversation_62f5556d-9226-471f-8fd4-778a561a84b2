#!/usr/bin/env node

/**
 * Test Bootstrap Fix Script
 * This script tests if the Bootstrap CSS fix resolves the centering issue
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Bootstrap CSS fix...')

// 1. Check if nuxt.config.cloudflare.js has the correct Bootstrap CSS imports
function checkCloudflareConfig() {
  console.log('📋 Checking Cloudflare configuration...')
  
  const configPath = path.join(process.cwd(), 'nuxt.config.cloudflare.js')
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ nuxt.config.cloudflare.js not found')
    return false
  }
  
  const content = fs.readFileSync(configPath, 'utf8')
  
  // Check for Bootstrap CSS import
  const hasBootstrapCSS = content.includes("'bootstrap/dist/css/bootstrap.css'")
  const hasBootstrapVueCSS = content.includes("'bootstrap-vue/dist/bootstrap-vue.css'")
  const hasBootstrapVueConfig = content.includes('bootstrapVue:')
  const hasTranspileConfig = content.includes("'bootstrap-vue'")
  
  console.log(`   - Bootstrap CSS: ${hasBootstrapCSS ? '✅' : '❌'}`)
  console.log(`   - Bootstrap Vue CSS: ${hasBootstrapVueCSS ? '✅' : '❌'}`)
  console.log(`   - Bootstrap Vue Config: ${hasBootstrapVueConfig ? '✅' : '❌'}`)
  console.log(`   - Transpile Config: ${hasTranspileConfig ? '✅' : '❌'}`)
  
  return hasBootstrapCSS && hasBootstrapVueCSS && hasBootstrapVueConfig && hasTranspileConfig
}

// 2. Compare with main config to ensure consistency
function compareConfigs() {
  console.log('🔍 Comparing configurations...')
  
  const mainConfigPath = path.join(process.cwd(), 'nuxt.config.js')
  const cloudflareConfigPath = path.join(process.cwd(), 'nuxt.config.cloudflare.js')
  
  if (!fs.existsSync(mainConfigPath) || !fs.existsSync(cloudflareConfigPath)) {
    console.error('❌ Configuration files not found')
    return false
  }
  
  const mainContent = fs.readFileSync(mainConfigPath, 'utf8')
  const cloudflareContent = fs.readFileSync(cloudflareConfigPath, 'utf8')
  
  // Check if both have Bootstrap CSS
  const mainHasBootstrap = mainContent.includes("'bootstrap/dist/css/bootstrap.css'")
  const cloudflareHasBootstrap = cloudflareContent.includes("'bootstrap/dist/css/bootstrap.css'")
  
  // Check if both have Bootstrap Vue config
  const mainHasBootstrapVueConfig = mainContent.includes('bootstrapVue:')
  const cloudflareHasBootstrapVueConfig = cloudflareContent.includes('bootstrapVue:')
  
  console.log(`   - Main config Bootstrap CSS: ${mainHasBootstrap ? '✅' : '❌'}`)
  console.log(`   - Cloudflare config Bootstrap CSS: ${cloudflareHasBootstrap ? '✅' : '❌'}`)
  console.log(`   - Main config Bootstrap Vue: ${mainHasBootstrapVueConfig ? '✅' : '❌'}`)
  console.log(`   - Cloudflare config Bootstrap Vue: ${cloudflareHasBootstrapVueConfig ? '✅' : '❌'}`)
  
  const isConsistent = mainHasBootstrap === cloudflareHasBootstrap && 
                      mainHasBootstrapVueConfig === cloudflareHasBootstrapVueConfig
  
  if (isConsistent) {
    console.log('✅ Configurations are consistent')
  } else {
    console.error('❌ Configuration inconsistency detected')
  }
  
  return isConsistent
}

// 3. Check if the layout classes are properly defined in the main component
function checkLayoutClasses() {
  console.log('🎨 Checking layout classes...')
  
  const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
  
  if (!fs.existsSync(indexPath)) {
    console.error('❌ pages/index.vue not found')
    return false
  }
  
  const content = fs.readFileSync(indexPath, 'utf8')
  
  // Check for key Bootstrap classes
  const hasContainerFluid = content.includes('container-fluid')
  const hasRowJustifyCenter = content.includes('row justify-content-center')
  const hasColLg6 = content.includes('col-lg-6')
  const hasPreviewPanelCenter = content.includes('justify-content: center') && content.includes('align-items: center')
  
  console.log(`   - Container Fluid: ${hasContainerFluid ? '✅' : '❌'}`)
  console.log(`   - Row Justify Center: ${hasRowJustifyCenter ? '✅' : '❌'}`)
  console.log(`   - Col LG 6: ${hasColLg6 ? '✅' : '❌'}`)
  console.log(`   - Preview Panel Centering: ${hasPreviewPanelCenter ? '✅' : '❌'}`)
  
  return hasContainerFluid && hasRowJustifyCenter && hasColLg6 && hasPreviewPanelCenter
}

// 4. Generate a summary report
function generateReport() {
  console.log('\n📊 Bootstrap Fix Test Report')
  console.log('=' .repeat(50))
  
  const configCheck = checkCloudflareConfig()
  const consistencyCheck = compareConfigs()
  const layoutCheck = checkLayoutClasses()
  
  console.log('\n📋 Summary:')
  console.log(`   - Cloudflare Config: ${configCheck ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`   - Config Consistency: ${consistencyCheck ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`   - Layout Classes: ${layoutCheck ? '✅ PASS' : '❌ FAIL'}`)
  
  const allPassed = configCheck && consistencyCheck && layoutCheck
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! The Bootstrap fix should resolve the centering issue.')
    console.log('💡 Next steps:')
    console.log('   1. Run: npm run build:cloudflare-simple')
    console.log('   2. Deploy to Cloudflare Pages')
    console.log('   3. Test the live site to confirm the fix')
  } else {
    console.log('\n⚠️  Some tests failed. Please review the issues above.')
  }
  
  return allPassed
}

// Run the test
if (require.main === module) {
  const success = generateReport()
  process.exit(success ? 0 : 1)
}

module.exports = {
  checkCloudflareConfig,
  compareConfigs,
  checkLayoutClasses,
  generateReport
}
