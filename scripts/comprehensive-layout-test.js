#!/usr/bin/env node

/**
 * 综合布局测试脚本
 * 测试小屏幕溢出、居中布局和性能优化的修复效果
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 开始综合布局测试...')

class LayoutTester {
  constructor() {
    this.testResults = {
      smallScreenOverflow: { passed: false, issues: [] },
      centeringLayout: { passed: false, issues: [] },
      performanceOptimization: { passed: false, issues: [] }
    }
  }

  async runAllTests() {
    console.log('📱 测试1: 小屏幕布局溢出问题')
    this.testSmallScreenOverflow()
    
    console.log('\n🎯 测试2: 居中布局问题')
    this.testCenteringLayout()
    
    console.log('\n🚀 测试3: 性能优化')
    this.testPerformanceOptimization()
    
    console.log('\n📊 生成测试报告')
    this.generateReport()
  }

  testSmallScreenOverflow() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    
    if (!fs.existsSync(indexPath)) {
      this.testResults.smallScreenOverflow.issues.push('index.vue文件不存在')
      return
    }

    const content = fs.readFileSync(indexPath, 'utf8')
    
    // 检查小屏幕媒体查询
    const hasSmallScreenQuery = content.includes('@media (max-width: 480px)')
    const hasExtraSmallQuery = content.includes('@media (max-width: 576px)')
    const hasOverflowHidden = content.includes('overflow-x: hidden')
    const hasBoxSizing = content.includes('box-sizing: border-box')
    const hasMaxWidth100vw = content.includes('max-width: 100vw')
    const hasResponsiveButtonGroup = content.includes('button-group') && content.includes('flex-wrap: wrap')

    console.log(`   - 小屏幕媒体查询 (480px): ${hasSmallScreenQuery ? '✅' : '❌'}`)
    console.log(`   - 超小屏幕媒体查询 (576px): ${hasExtraSmallQuery ? '✅' : '❌'}`)
    console.log(`   - 水平溢出控制: ${hasOverflowHidden ? '✅' : '❌'}`)
    console.log(`   - 盒模型设置: ${hasBoxSizing ? '✅' : '❌'}`)
    console.log(`   - 视口宽度限制: ${hasMaxWidth100vw ? '✅' : '❌'}`)
    console.log(`   - 响应式按钮组: ${hasResponsiveButtonGroup ? '✅' : '❌'}`)

    if (!hasSmallScreenQuery) {
      this.testResults.smallScreenOverflow.issues.push('缺少480px断点的媒体查询')
    }
    if (!hasExtraSmallQuery) {
      this.testResults.smallScreenOverflow.issues.push('缺少576px断点的媒体查询')
    }
    if (!hasOverflowHidden) {
      this.testResults.smallScreenOverflow.issues.push('缺少水平溢出控制')
    }
    if (!hasBoxSizing) {
      this.testResults.smallScreenOverflow.issues.push('缺少盒模型设置')
    }
    if (!hasMaxWidth100vw) {
      this.testResults.smallScreenOverflow.issues.push('缺少视口宽度限制')
    }
    if (!hasResponsiveButtonGroup) {
      this.testResults.smallScreenOverflow.issues.push('按钮组缺少响应式布局')
    }

    this.testResults.smallScreenOverflow.passed = 
      hasSmallScreenQuery && hasExtraSmallQuery && hasOverflowHidden && 
      hasBoxSizing && hasMaxWidth100vw && hasResponsiveButtonGroup
  }

  testCenteringLayout() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    const content = fs.readFileSync(indexPath, 'utf8')

    // 检查居中布局相关的CSS
    const hasFlexJustifyCenter = content.includes('justify-content: center')
    const hasAlignItemsCenter = content.includes('align-items: center')
    const hasMarginAuto = content.includes('margin: 0 auto')
    const hasContainerFluidFlex = content.includes('.card-editor-section .container-fluid') && 
                                  content.includes('display: flex')
    const hasRowFlex = content.includes('.card-editor-section .row') && 
                       content.includes('display: flex')
    const hasColFlexDirection = content.includes('.card-editor-section .col-lg-6') && 
                                content.includes('flex-direction: column')
    const hasLargeScreenCentering = content.includes('@media (min-width: 992px)') && 
                                    content.includes('justify-content: center')

    console.log(`   - Flex居中对齐: ${hasFlexJustifyCenter ? '✅' : '❌'}`)
    console.log(`   - 垂直居中对齐: ${hasAlignItemsCenter ? '✅' : '❌'}`)
    console.log(`   - 自动边距居中: ${hasMarginAuto ? '✅' : '❌'}`)
    console.log(`   - 容器Flex布局: ${hasContainerFluidFlex ? '✅' : '❌'}`)
    console.log(`   - 行Flex布局: ${hasRowFlex ? '✅' : '❌'}`)
    console.log(`   - 列Flex方向: ${hasColFlexDirection ? '✅' : '❌'}`)
    console.log(`   - 大屏幕居中: ${hasLargeScreenCentering ? '✅' : '❌'}`)

    if (!hasFlexJustifyCenter) {
      this.testResults.centeringLayout.issues.push('缺少Flex水平居中')
    }
    if (!hasAlignItemsCenter) {
      this.testResults.centeringLayout.issues.push('缺少Flex垂直居中')
    }
    if (!hasMarginAuto) {
      this.testResults.centeringLayout.issues.push('缺少自动边距居中')
    }
    if (!hasContainerFluidFlex) {
      this.testResults.centeringLayout.issues.push('容器缺少Flex布局')
    }
    if (!hasRowFlex) {
      this.testResults.centeringLayout.issues.push('行缺少Flex布局')
    }
    if (!hasColFlexDirection) {
      this.testResults.centeringLayout.issues.push('列缺少Flex方向设置')
    }
    if (!hasLargeScreenCentering) {
      this.testResults.centeringLayout.issues.push('大屏幕缺少居中设置')
    }

    this.testResults.centeringLayout.passed = 
      hasFlexJustifyCenter && hasAlignItemsCenter && hasMarginAuto && 
      hasContainerFluidFlex && hasRowFlex && hasColFlexDirection && hasLargeScreenCentering
  }

  testPerformanceOptimization() {
    const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
    const performanceOptimizerPath = path.join(process.cwd(), 'components', 'PerformanceOptimizer.vue')
    const optimizedImagePath = path.join(process.cwd(), 'components', 'OptimizedImage.vue')

    // 检查性能优化组件是否存在
    const hasPerformanceOptimizer = fs.existsSync(performanceOptimizerPath)
    const hasOptimizedImage = fs.existsSync(optimizedImagePath)

    console.log(`   - 性能优化组件: ${hasPerformanceOptimizer ? '✅' : '❌'}`)
    console.log(`   - 优化图片组件: ${hasOptimizedImage ? '✅' : '❌'}`)

    if (fs.existsSync(indexPath)) {
      const content = fs.readFileSync(indexPath, 'utf8')
      
      // 检查是否集成了性能优化组件
      const hasPerformanceOptimizerUsage = content.includes('<PerformanceOptimizer')
      const hasOptimizedImageUsage = content.includes('<OptimizedImage')
      const hasPerformanceMethods = content.includes('onCriticalCSSLoaded') && 
                                    content.includes('preloadCriticalResources')

      console.log(`   - 性能优化组件使用: ${hasPerformanceOptimizerUsage ? '✅' : '❌'}`)
      console.log(`   - 优化图片组件使用: ${hasOptimizedImageUsage ? '✅' : '❌'}`)
      console.log(`   - 性能优化方法: ${hasPerformanceMethods ? '✅' : '❌'}`)

      if (!hasPerformanceOptimizerUsage) {
        this.testResults.performanceOptimization.issues.push('未使用性能优化组件')
      }
      if (!hasOptimizedImageUsage) {
        this.testResults.performanceOptimization.issues.push('未使用优化图片组件')
      }
      if (!hasPerformanceMethods) {
        this.testResults.performanceOptimization.issues.push('缺少性能优化方法')
      }

      this.testResults.performanceOptimization.passed = 
        hasPerformanceOptimizer && hasOptimizedImage && hasPerformanceOptimizerUsage && 
        hasOptimizedImageUsage && hasPerformanceMethods
    }

    if (!hasPerformanceOptimizer) {
      this.testResults.performanceOptimization.issues.push('性能优化组件文件不存在')
    }
    if (!hasOptimizedImage) {
      this.testResults.performanceOptimization.issues.push('优化图片组件文件不存在')
    }
  }

  generateReport() {
    console.log('\n📋 综合测试报告')
    console.log('=' .repeat(60))

    const tests = [
      { name: '小屏幕布局溢出', key: 'smallScreenOverflow', icon: '📱' },
      { name: '居中布局', key: 'centeringLayout', icon: '🎯' },
      { name: '性能优化', key: 'performanceOptimization', icon: '🚀' }
    ]

    let allPassed = true

    tests.forEach(test => {
      const result = this.testResults[test.key]
      const status = result.passed ? '✅ PASS' : '❌ FAIL'
      
      console.log(`\n${test.icon} ${test.name}: ${status}`)
      
      if (!result.passed) {
        allPassed = false
        console.log('   问题:')
        result.issues.forEach((issue, index) => {
          console.log(`   ${index + 1}. ${issue}`)
        })
      }
    })

    console.log('\n📊 总体结果:')
    if (allPassed) {
      console.log('🎉 所有测试通过！布局问题已成功修复。')
      console.log('\n💡 建议的下一步操作:')
      console.log('   1. 运行构建: npm run build:cloudflare-simple')
      console.log('   2. 部署到Cloudflare Pages')
      console.log('   3. 在不同设备上测试实际效果')
      console.log('   4. 运行性能分析: npm run analyze:performance')
    } else {
      console.log('⚠️  部分测试失败，请查看上述问题并进行修复。')
    }

    // 保存详细报告
    const reportPath = path.join(process.cwd(), 'layout-test-report.json')
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      allPassed,
      results: this.testResults
    }, null, 2))

    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
    
    return allPassed
  }
}

// 运行测试
if (require.main === module) {
  const tester = new LayoutTester()
  tester.runAllTests().then(() => {
    process.exit(0)
  }).catch(error => {
    console.error('测试过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = LayoutTester
