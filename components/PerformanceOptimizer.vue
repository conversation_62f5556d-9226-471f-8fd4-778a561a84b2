<template>
  <div class="performance-optimizer">
    <!-- 关键CSS预加载 -->
    <link
      v-if="shouldPreloadCriticalCSS"
      rel="preload"
      href="/css/critical/above-fold.css"
      as="style"
      @load="onCriticalCSSLoad"
    />
    
    <!-- 字体预加载 -->
    <link
      v-for="font in criticalFonts"
      :key="font.name"
      rel="preload"
      :href="font.url"
      as="font"
      type="font/ttf"
      crossorigin="anonymous"
    />
    
    <!-- 图片预加载 -->
    <link
      v-for="image in criticalImages"
      :key="image.url"
      rel="preload"
      :href="image.url"
      as="image"
    />
    
    <!-- 性能监控 -->
    <div v-if="showPerformanceMetrics" class="performance-metrics">
      <div class="metric">
        <span>FCP: {{ metrics.fcp }}ms</span>
      </div>
      <div class="metric">
        <span>LCP: {{ metrics.lcp }}ms</span>
      </div>
      <div class="metric">
        <span>FID: {{ metrics.fid }}ms</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PerformanceOptimizer',
  data() {
    return {
      shouldPreloadCriticalCSS: true,
      showPerformanceMetrics: process.env.NODE_ENV === 'development',
      metrics: {
        fcp: 0,
        lcp: 0,
        fid: 0,
        cls: 0
      },
      criticalFonts: [
        {
          name: 'MatrixBoldSmallCaps',
          url: '/fonts/MatrixBoldSmallCaps.ttf'
        },
        {
          name: 'en',
          url: '/fonts/en.ttf'
        }
      ],
      criticalImages: [
        {
          url: '/images/logo.png',
          priority: 'high'
        }
      ]
    }
  },
  mounted() {
    this.initPerformanceMonitoring()
    this.optimizeResourceLoading()
  },
  methods: {
    initPerformanceMonitoring() {
      if (typeof window === 'undefined' || !window.performance) return
      
      // 监控 First Contentful Paint
      this.observeFCP()
      
      // 监控 Largest Contentful Paint
      this.observeLCP()
      
      // 监控 First Input Delay
      this.observeFID()
      
      // 监控 Cumulative Layout Shift
      this.observeCLS()
    },
    
    observeFCP() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
          if (fcpEntry) {
            this.metrics.fcp = Math.round(fcpEntry.startTime)
            this.reportMetric('FCP', this.metrics.fcp)
          }
        })
        observer.observe({ entryTypes: ['paint'] })
      }
    },
    
    observeLCP() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.metrics.lcp = Math.round(lastEntry.startTime)
          this.reportMetric('LCP', this.metrics.lcp)
        })
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
      }
    },
    
    observeFID() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            this.metrics.fid = Math.round(entry.processingStart - entry.startTime)
            this.reportMetric('FID', this.metrics.fid)
          })
        })
        observer.observe({ entryTypes: ['first-input'] })
      }
    },
    
    observeCLS() {
      if ('PerformanceObserver' in window) {
        let clsValue = 0
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          this.metrics.cls = Math.round(clsValue * 1000) / 1000
          this.reportMetric('CLS', this.metrics.cls)
        })
        observer.observe({ entryTypes: ['layout-shift'] })
      }
    },
    
    reportMetric(name, value) {
      // 发送到 Google Analytics
      if (typeof gtag === 'function') {
        gtag('event', 'web_vitals', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(value),
          non_interaction: true
        })
      }
      
      // 开发环境下输出到控制台
      if (process.env.NODE_ENV === 'development') {
        console.log(`Performance Metric - ${name}: ${value}`)
      }
    },
    
    optimizeResourceLoading() {
      // 延迟加载非关键资源
      this.$nextTick(() => {
        this.lazyLoadNonCriticalResources()
      })
    },
    
    lazyLoadNonCriticalResources() {
      // 延迟加载非关键字体
      setTimeout(() => {
        this.loadNonCriticalFonts()
      }, 1000)
      
      // 延迟加载非关键图片
      setTimeout(() => {
        this.loadNonCriticalImages()
      }, 2000)
    },
    
    loadNonCriticalFonts() {
      const nonCriticalFonts = [
        '/fonts/zh.ttf',
        '/fonts/jp.ttf',
        '/fonts/jp2.otf'
      ]
      
      nonCriticalFonts.forEach(fontUrl => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.href = fontUrl
        link.as = 'font'
        link.type = 'font/ttf'
        link.crossOrigin = 'anonymous'
        document.head.appendChild(link)
      })
    },
    
    loadNonCriticalImages() {
      // 这里可以预加载一些可能需要的图片
      const nonCriticalImages = [
        '/images/background.jpg',
        '/images/card-template.png'
      ]
      
      nonCriticalImages.forEach(imageUrl => {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = imageUrl
        document.head.appendChild(link)
      })
    },
    
    onCriticalCSSLoad() {
      // 关键CSS加载完成后的回调
      this.$emit('critical-css-loaded')
    }
  }
}
</script>

<style scoped>
.performance-optimizer {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 9999;
  pointer-events: none;
}

.performance-metrics {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  pointer-events: auto;
}

.metric {
  margin: 2px 0;
}

@media (max-width: 768px) {
  .performance-metrics {
    display: none;
  }
}
</style>
