<template>
  <div class="optimized-image-container" :style="containerStyle">
    <!-- 主图片 -->
    <img
      v-if="loaded && !error"
      :src="currentSrc"
      :alt="alt"
      :class="['optimized-image', imageClass]"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
      :loading="lazy ? 'lazy' : 'eager'"
      :decoding="async ? 'async' : 'sync'"
    />
    
    <!-- 加载占位符 -->
    <div
      v-else-if="loading"
      class="image-placeholder"
      :style="placeholderStyle"
    >
      <div class="loading-skeleton"></div>
      <div v-if="showLoadingText" class="loading-text">
        {{ loadingText || 'Loading...' }}
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div
      v-else-if="error"
      class="image-error"
      :style="placeholderStyle"
    >
      <fa :icon="['fas', 'image']" class="error-icon" />
      <span class="error-text">{{ errorText || 'Failed to load image' }}</span>
      <button v-if="allowRetry" @click="retry" class="retry-btn">
        Retry
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OptimizedImage',
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: null
    },
    height: {
      type: [String, Number],
      default: null
    },
    imageClass: {
      type: String,
      default: ''
    },
    errorText: {
      type: String,
      default: null
    },
    loadingText: {
      type: String,
      default: null
    },
    lazy: {
      type: Boolean,
      default: true
    },
    async: {
      type: Boolean,
      default: true
    },
    showLoadingText: {
      type: Boolean,
      default: false
    },
    allowRetry: {
      type: Boolean,
      default: true
    },
    // WebP支持
    webpSrc: {
      type: String,
      default: null
    },
    // 响应式图片源
    srcset: {
      type: String,
      default: null
    },
    sizes: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loaded: false,
      loading: false,
      error: false,
      observer: null,
      retryCount: 0,
      maxRetries: 3
    }
  },
  computed: {
    containerStyle() {
      const style = {}
      if (this.width) style.width = typeof this.width === 'number' ? `${this.width}px` : this.width
      if (this.height) style.height = typeof this.height === 'number' ? `${this.height}px` : this.height
      return style
    },
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        transition: 'opacity 0.3s ease'
      }
    },
    placeholderStyle() {
      return {
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px'
      }
    },
    currentSrc() {
      // 优先使用WebP格式（如果支持）
      if (this.webpSrc && this.supportsWebP()) {
        return this.webpSrc
      }
      return this.src
    }
  },
  mounted() {
    if (this.lazy && 'IntersectionObserver' in window) {
      this.setupIntersectionObserver()
    } else {
      this.loadImage()
    }
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    setupIntersectionObserver() {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              this.loadImage()
              this.observer.unobserve(entry.target)
            }
          })
        },
        {
          rootMargin: '50px',
          threshold: 0.1
        }
      )
      this.observer.observe(this.$el)
    },
    
    loadImage() {
      if (this.loaded || this.loading) return
      
      this.loading = true
      this.error = false
      
      const img = new Image()
      
      // 设置srcset和sizes（如果提供）
      if (this.srcset) img.srcset = this.srcset
      if (this.sizes) img.sizes = this.sizes
      
      img.onload = () => {
        this.loaded = true
        this.loading = false
        this.$emit('load', {
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      }
      
      img.onerror = () => {
        this.loading = false
        if (this.retryCount < this.maxRetries) {
          // 自动重试
          setTimeout(() => {
            this.retryCount++
            this.loadImage()
          }, 1000 * this.retryCount)
        } else {
          this.error = true
          this.$emit('error')
        }
      }
      
      img.src = this.currentSrc
    },
    
    retry() {
      this.retryCount = 0
      this.error = false
      this.loadImage()
    },
    
    onLoad() {
      this.$emit('load')
    },
    
    onError() {
      this.$emit('error')
    },
    
    supportsWebP() {
      // 检测WebP支持
      if (typeof window === 'undefined') return false
      
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
    }
  }
}
</script>

<style scoped>
.optimized-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f8f9fa;
}

.optimized-image {
  display: block;
  max-width: 100%;
  height: auto;
}

.image-placeholder {
  min-height: 100px;
}

.loading-skeleton {
  width: 60%;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.loading-text {
  font-size: 12px;
  color: #6c757d;
  margin-top: 8px;
}

.image-error {
  color: #6c757d;
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.error-text {
  display: block;
  font-size: 12px;
  margin-bottom: 8px;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .loading-skeleton {
    height: 16px;
  }
  
  .error-icon {
    font-size: 20px;
  }
}
</style>
