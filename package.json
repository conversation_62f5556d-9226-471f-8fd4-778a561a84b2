{"name": "yugioh_card_maker", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt build", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt start", "generate": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt generate && npm run sitemap", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint": "npm run lint:js", "build:gh-pages": "cross-env DEPLOY_ENV=GH_PAGES NODE_OPTIONS=--openssl-legacy-provider nuxt build", "generate:gh-pages": "cross-env DEPLOY_ENV=GH_PAGES NODE_OPTIONS=--openssl-legacy-provider nuxt generate", "vercel-build": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt generate", "build:cloudflare": "cross-env DEPLOY_ENV=CLOUDFLARE_PAGES NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096 nuxt generate --config-file nuxt.config.cloudflare.js && npm run sitemap && npm run copy:redirects && npm run optimize:cloudflare && npm run fix:deployment", "build:cloudflare-simple": "cross-env NODE_OPTIONS=--openssl-legacy-provider nuxt generate --config-file nuxt.config.cloudflare.js", "fix:deployment": "node scripts/fix-deployment-issues.js", "verify:deployment": "node scripts/verify-deployment.js", "check:final": "node scripts/final-check.js", "test:bootstrap-fix": "node scripts/test-bootstrap-fix.js", "analyze:performance": "node scripts/performance-optimizer.js", "test:layout": "node scripts/comprehensive-layout-test.js", "verify:webp": "node scripts/verify-webp-migration.js", "copy:redirects": "cp static/_redirects dist/_redirects", "optimize:cloudflare": "node scripts/optimize-for-cloudflare.js", "sitemap": "node scripts/generate-sitemap.js", "sitemap:validate": "node scripts/validate-sitemap.js", "seo:validate": "node scripts/seo-validation.js", "build:full": "npm run generate && npm run sitemap && cp static/sitemap.xml dist/sitemap.xml && npm run sitemap:validate && npm run seo:validate"}, "dependencies": {"@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/i18n": "^7.3.1", "@nuxtjs/sitemap": "^2.4.0", "bootstrap": "^4.6.0", "bootstrap-vue": "^2.21.2", "core-js": "^3.15.1", "cross-env": "^7.0.3", "nuxt": "^2.15.7", "nuxt-font-loader": "^1.1.3", "nuxt-fontawesome": "^0.4.0"}, "devDependencies": {"@babel/eslint-parser": "^7.14.7", "@nuxtjs/eslint-config": "^6.0.1", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/vercel-builder": "^0.24.0", "autoprefixer": "^10.4.21", "cssnano": "^7.1.0", "eslint": "^7.29.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-vue": "^7.12.1", "postcss-preset-env": "^10.2.4", "prettier": "^2.3.2", "xmldom": "^0.6.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}